@import "@/styles/variables.module";

.modalRoot {
  :global {
    .ant-modal-content {
      padding: 28px !important;
      border-radius: 28px !important;
      border: 1px solid #ededed;
      display: flex;
      flex-direction: column;
    }

    .ant-modal-body {
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    .ant-modal-header {
      margin-bottom: 20px !important;
      padding: 0 !important;
    }

    .ant-modal-close {
      top: 28px !important;
      right: 28px !important;
    }

    .ant-form-item-label {
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
    }

    textarea.ant-input {
      font-size: 16px;
      font-weight: 400;
      line-height: 24px;
      padding: 12px;
    }

    .ant-select-selection-placeholder {
      color: #505050 !important;
    }
  }

  .labelSubtitle {
    color: $gray;
    font-weight: 400;
  }

  .infoIcon {
    color: #007aff;
    font-size: 24px;
  }
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.titleContainer {
  display: flex;
  align-items: center;
  gap: 8px;
}

.iconCircle {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 44px;
  height: 44px;
  border-radius: 14.67px;
  border: 1.22px solid #e3e3e3;
  box-shadow: 0px 0px 4.89px 0px rgba(159, 159, 159, 0.25);
}

.modalTitle {
  font-family: "Plus Jakarta Sans", sans-serif;
  font-weight: 700;
  font-size: 20px;
  line-height: 1.3;
  color: #1c1c1c;
  margin: 0;
}

.closeIconContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.form {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.optionsContainer {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: flex-start;
  justify-content: flex-start;
}

.input {
  border-radius: 20px !important;
  padding: 13px 14px !important;
  font-size: 16px !important;

  &:focus,
  &:hover {
    border-color: #7f56d9 !important;
    box-shadow: 0px 0px 12px 0px rgba(127, 86, 217, 0.3) !important;
  }
}

.buttonContainer {
  margin-top: 20px;
  margin-bottom: 0;
}

.createButton {
  width: 100%;
  height: 64px !important;
  background-color: #8b5cf6 !important;
  border: none !important;
  border-radius: 16px !important;
  font-family: "Plus Jakarta Sans", sans-serif !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  line-height: 1.5 !important;
  color: #ffffff !important;
  box-shadow: none !important;
  text-transform: none !important;

  &:hover {
    background-color: #7c3aed !important;
    color: #ffffff !important;
  }
}

.progressContainer {
  margin-top: 20px;
}

.bx {
  font-size: 24px;
  cursor: pointer;
  color: $gray;
}
