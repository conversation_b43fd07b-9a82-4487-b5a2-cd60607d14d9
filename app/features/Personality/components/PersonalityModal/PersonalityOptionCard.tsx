import React, { useState } from 'react';
import { Box, Typography, Paper } from '@mui/material';

import PersonalityIcon from './../../../../../public/personality-icon.svg';

interface PersonalityOptionCardProps {
  title: string;
  description: string;
  iconSrc: string;
  isSelected?: boolean;
  onClick: () => void;
  variant?: 'primary' | 'secondary';
}

// Custom AI icon component based on the Figma design
const AIIcon: React.FC<React.SVGProps<SVGSVGElement>> = ({ ...props }) => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <rect x="1.5" y="1.5" width="17" height="17" rx="2.5" stroke="currentColor" strokeWidth="1.5"/>
  </svg>
);

const iconMap: Record<string, React.FC<React.SVGProps<SVGSVGElement>>> = {
  '/images/personality-icon.svg': PersonalityIcon,
  '/images/sparkles.svg': AIIcon,
};

const PersonalityOptionCard: React.FC<PersonalityOptionCardProps> = ({
  title,
  description,
  iconSrc,
  isSelected = false,
  onClick,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const IconComponent = iconMap[iconSrc];

  // Determine if this card should show selected/active styling
  const isActive = isSelected || isHovered;

  return (
    <Paper
      onClick={onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      elevation={0}
      sx={{
        border: isSelected ? '1px solid #8B5CF6' : '1px solid #E5E7EB',
        borderRadius: '20px',
        background: '#fff',
        p: '14px',
        display: 'flex',
        alignItems: 'center',
        gap: 2,
        cursor: 'pointer',
        transition: 'all 0.2s ease',
        boxShadow: isSelected ? '0px 0px 12px 0px rgba(127, 86, 217, 0.30)' : 'none',
        overflow: 'hidden',
        '&:hover': {
          borderColor: '#8B5CF6',
          boxShadow: '0px 0px 12px 0px rgba(127, 86, 217, 0.30)',
        },
      }}
    >
      <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', alignItems: 'flex-start', gap: isSelected ? 1 : 3 }}>
        <Box
          sx={{
            p: 2,
            backgroundColor: isActive ? 'rgba(139, 92, 246, 0.1)' : '#F5F5F5',
            borderRadius: '12px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            transition: 'all 0.2s ease',
          }}
        >
          {IconComponent ? (
            <IconComponent
              width={24}
              height={24}
              style={{
                color: isActive ? '#8B5CF6' : '#6B7280',
                fill: iconSrc === '/images/sparkles.svg' ? 'none' : (isActive ? '#8B5CF6' : '#6B7280'),
                stroke: iconSrc === '/images/sparkles.svg' ? (isActive ? '#8B5CF6' : '#6B7280') : 'none',
              }}
            />
          ) : null}
        </Box>

        {isSelected ? (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, alignSelf: 'stretch' }}>
            <Typography sx={{
              fontWeight: 600,
              fontFamily: 'Plus Jakarta Sans',
              fontSize: 16,
              color: '#18181B',
              lineHeight: 'normal'
            }}>
              {title}
            </Typography>
            <Typography sx={{
              fontWeight: 400,
              fontFamily: 'Plus Jakarta Sans',
              fontSize: 14,
              color: '#18181B',
              lineHeight: 'normal',
              alignSelf: 'stretch'
            }}>
              {description}
            </Typography>
          </Box>
        ) : (
          <Typography sx={{
            fontWeight: 600,
            fontFamily: 'Plus Jakarta Sans',
            fontSize: 16,
            color: '#18181B',
            lineHeight: 'normal'
          }}>
            {title}
          </Typography>
        )}
      </Box>
    </Paper>
  );
};

export default PersonalityOptionCard;
